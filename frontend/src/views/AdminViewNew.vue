<template>
  <div class="admin-view-new">
    <!-- Authorization Check -->
    <div v-if="!isDashboardAuthorized" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-base-200/30 to-base-300/20">
      <div class="text-center space-y-6 max-w-md mx-auto p-8">
        <div class="text-8xl">🔒</div>
        <h2 class="text-3xl font-bold text-base-content">Access Restricted</h2>
        <div class="space-y-2">
          <p class="text-base-content/70" v-if="!authStore.isAuthenticated">
            Please log in to access the admin dashboard.
          </p>
          <p class="text-base-content/70" v-else-if="!authStore.isAdmin">
            Admin privileges required to access this dashboard.
          </p>
          <p class="text-base-content/70" v-else-if="authStore.isLocked">
            Session is locked. Please unlock to continue.
          </p>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link 
            v-if="!authStore.isAuthenticated"
            to="/admin/login" 
            class="btn btn-primary"
          >
            <Icon name="login" size="sm" class="mr-2" />
            Admin Login
          </router-link>
          
          <router-link 
            v-if="authStore.isAuthenticated && !authStore.isAdmin"
            to="/dashboard" 
            class="btn btn-secondary"
          >
            <Icon name="home" size="sm" class="mr-2" />
            User Dashboard
          </router-link>
          
          <button 
            v-if="authStore.isLocked"
            @click="unlockSession"
            class="btn btn-primary"
            :disabled="isUnlocking"
          >
            <span v-if="isUnlocking" class="loading loading-spinner loading-sm mr-2"></span>
            <Icon v-else name="unlock" size="sm" class="mr-2" />
            Unlock Session
          </button>
        </div>
      </div>
    </div>

    <!-- Admin Layout -->
    <AdminLayoutNew v-else />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import AdminLayoutNew from '@/components/admin/AdminLayoutNew.vue'
import Icon from '@/components/common/Icon.vue'

// Store
const authStore = useAuthStore()

// State
const isUnlocking = ref(false)

// Computed
const isDashboardAuthorized = computed(() => {
  return authStore.isAuthenticated && authStore.isAdmin && !authStore.isLocked
})

// Methods
const unlockSession = async () => {
  try {
    isUnlocking.value = true
    // This will trigger the session unlock modal
    await authStore.checkSessionLock()
  } catch (error) {
    console.error('Failed to unlock session:', error)
  } finally {
    isUnlocking.value = false
  }
}
</script>

<style scoped>
.admin-view-new {
  /* Add any specific styles for the new admin view */
}
</style>
