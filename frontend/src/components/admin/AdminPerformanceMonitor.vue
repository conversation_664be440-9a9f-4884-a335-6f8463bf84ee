<template>
  <div class="admin-performance-monitor">
    <!-- Performance Stats Card -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title text-lg">
            <Icon name="chart-bar" size="md" class="text-primary mr-2" />
            Performance Monitor
          </h3>
          <button 
            @click="refreshStats"
            class="btn btn-sm btn-ghost"
            :disabled="isRefreshing"
          >
            <span v-if="isRefreshing" class="loading loading-spinner loading-sm"></span>
            <Icon v-else name="refresh" size="sm" />
          </button>
        </div>

        <!-- Performance Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Chunk Loading Times -->
          <div class="stat bg-base-200/50 rounded-lg p-4">
            <div class="stat-figure text-primary">
              <Icon name="clock" size="lg" />
            </div>
            <div class="stat-title text-sm">Avg Load Time</div>
            <div class="stat-value text-lg">{{ averageLoadTime }}ms</div>
            <div class="stat-desc">{{ totalChunksLoaded }} chunks loaded</div>
          </div>

          <!-- Memory Usage -->
          <div class="stat bg-base-200/50 rounded-lg p-4">
            <div class="stat-figure text-secondary">
              <Icon name="cpu-chip" size="lg" />
            </div>
            <div class="stat-title text-sm">Memory Usage</div>
            <div class="stat-value text-lg">{{ memoryUsage }}MB</div>
            <div class="stat-desc">{{ memoryPercentage }}% of available</div>
          </div>

          <!-- Bundle Size -->
          <div class="stat bg-base-200/50 rounded-lg p-4">
            <div class="stat-figure text-accent">
              <Icon name="archive-box" size="lg" />
            </div>
            <div class="stat-title text-sm">Bundle Size</div>
            <div class="stat-value text-lg">{{ bundleSize }}KB</div>
            <div class="stat-desc">{{ compressionRatio }}% compressed</div>
          </div>
        </div>

        <!-- Chunk Loading History -->
        <div class="mt-6">
          <h4 class="font-semibold mb-3 flex items-center">
            <Icon name="clock" size="sm" class="mr-2" />
            Recent Chunk Loads
          </h4>
          <div class="overflow-x-auto">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Module</th>
                  <th>Load Time</th>
                  <th>Size</th>
                  <th>Status</th>
                  <th>Timestamp</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="load in recentLoads" :key="load.id" class="hover">
                  <td>
                    <div class="flex items-center">
                      <Icon :name="load.icon" size="sm" class="mr-2 text-primary" />
                      {{ load.module }}
                    </div>
                  </td>
                  <td>
                    <span :class="[
                      'badge badge-sm',
                      load.loadTime < 500 ? 'badge-success' : 
                      load.loadTime < 1000 ? 'badge-warning' : 'badge-error'
                    ]">
                      {{ load.loadTime }}ms
                    </span>
                  </td>
                  <td>{{ load.size }}KB</td>
                  <td>
                    <span :class="[
                      'badge badge-sm',
                      load.status === 'success' ? 'badge-success' : 'badge-error'
                    ]">
                      {{ load.status }}
                    </span>
                  </td>
                  <td class="text-xs text-base-content/60">
                    {{ formatTime(load.timestamp) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Performance Tips -->
        <div class="mt-6 p-4 bg-info/10 rounded-lg border border-info/20">
          <h4 class="font-semibold text-info mb-2 flex items-center">
            <Icon name="light-bulb" size="sm" class="mr-2" />
            Performance Tips
          </h4>
          <ul class="text-sm text-base-content/70 space-y-1">
            <li>• Modules are loaded on-demand to reduce initial bundle size</li>
            <li>• Each admin section is code-split for optimal performance</li>
            <li>• Components are cached after first load</li>
            <li>• Use browser dev tools to analyze chunk loading</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Types
interface ChunkLoad {
  id: string
  module: string
  icon: string
  loadTime: number
  size: number
  status: 'success' | 'error'
  timestamp: Date
}

// State
const isRefreshing = ref(false)
const recentLoads = ref<ChunkLoad[]>([])
const performanceObserver = ref<PerformanceObserver | null>(null)

// Mock performance data (in a real app, this would come from actual performance monitoring)
const mockPerformanceData = ref({
  totalLoadTime: 0,
  totalChunksLoaded: 0,
  memoryUsage: 0,
  bundleSize: 0
})

// Computed
const averageLoadTime = computed(() => {
  if (mockPerformanceData.value.totalChunksLoaded === 0) return 0
  return Math.round(mockPerformanceData.value.totalLoadTime / mockPerformanceData.value.totalChunksLoaded)
})

const totalChunksLoaded = computed(() => mockPerformanceData.value.totalChunksLoaded)

const memoryUsage = computed(() => {
  // Get memory info if available
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return Math.round(memory.usedJSHeapSize / 1024 / 1024)
  }
  return mockPerformanceData.value.memoryUsage
})

const memoryPercentage = computed(() => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
  }
  return 65 // Mock percentage
})

const bundleSize = computed(() => {
  // Calculate total size of loaded chunks
  const totalSize = recentLoads.value.reduce((sum, load) => sum + load.size, 0)
  return totalSize || mockPerformanceData.value.bundleSize
})

const compressionRatio = computed(() => {
  // Mock compression ratio
  return 75
})

// Methods
const refreshStats = async () => {
  isRefreshing.value = true
  
  try {
    // Simulate API call to get performance stats
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update mock data
    mockPerformanceData.value = {
      totalLoadTime: Math.random() * 5000 + 1000,
      totalChunksLoaded: Math.floor(Math.random() * 10) + 5,
      memoryUsage: Math.random() * 50 + 20,
      bundleSize: Math.random() * 500 + 200
    }
    
    console.log('📊 Performance stats refreshed')
  } catch (error) {
    console.error('Failed to refresh performance stats:', error)
  } finally {
    isRefreshing.value = false
  }
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString()
}

const addChunkLoad = (module: string, icon: string, loadTime: number, size: number, status: 'success' | 'error' = 'success') => {
  const load: ChunkLoad = {
    id: Date.now().toString(),
    module,
    icon,
    loadTime,
    size,
    status,
    timestamp: new Date()
  }
  
  recentLoads.value.unshift(load)
  
  // Keep only last 10 loads
  if (recentLoads.value.length > 10) {
    recentLoads.value = recentLoads.value.slice(0, 10)
  }
  
  // Update totals
  mockPerformanceData.value.totalLoadTime += loadTime
  mockPerformanceData.value.totalChunksLoaded += 1
}

// Initialize performance monitoring
const initializePerformanceMonitoring = () => {
  // Add some mock initial data
  addChunkLoad('Dashboard', 'chart-bar', 450, 125)
  addChunkLoad('Users', 'users', 320, 89)
  addChunkLoad('System', 'cog', 280, 67)
  
  // Set up performance observer if available
  if ('PerformanceObserver' in window) {
    try {
      performanceObserver.value = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation' || entry.entryType === 'resource') {
            console.log('📊 Performance entry:', entry)
          }
        })
      })
      
      performanceObserver.value.observe({ entryTypes: ['navigation', 'resource'] })
    } catch (error) {
      console.warn('Performance Observer not supported:', error)
    }
  }
}

// Lifecycle
onMounted(() => {
  initializePerformanceMonitoring()
})

onUnmounted(() => {
  if (performanceObserver.value) {
    performanceObserver.value.disconnect()
  }
})

// Expose method for external use
defineExpose({
  addChunkLoad
})
</script>

<style scoped>
.admin-performance-monitor {
  /* Add any specific styles */
}
</style>
