<template>
  <div class="admin-module-wrapper">
    <!-- Module Header -->
    <div class="bg-gradient-to-r from-primary/5 via-secondary/5 to-accent/5 border border-base-300 rounded-t-xl p-4 sm:p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-primary/20 rounded-lg">
            <Icon :name="module?.icon || 'cog'" size="lg" class="text-primary" />
          </div>
          <div>
            <h2 class="text-xl sm:text-2xl font-bold text-base-content">{{ module?.name || 'Module' }}</h2>
            <p class="text-sm text-base-content/60">{{ module?.description || 'Loading...' }}</p>
          </div>
        </div>
        
        <!-- Module Actions -->
        <div class="flex items-center space-x-2">
          <button
            @click="refreshModule"
            class="btn btn-ghost btn-sm"
            :disabled="isRefreshing"
            title="Refresh Module"
          >
            <span v-if="isRefreshing" class="loading loading-spinner loading-sm"></span>
            <Icon v-else name="refresh" size="sm" />
          </button>
          
          <button
            @click="toggleFullscreen"
            class="btn btn-ghost btn-sm hidden sm:flex"
            title="Toggle Fullscreen"
          >
            <Icon :name="isFullscreen ? 'arrows-pointing-in' : 'arrows-pointing-out'" size="sm" />
          </button>
        </div>
      </div>
    </div>

    <!-- Module Content -->
    <div 
      :class="[
        'bg-base-100 border-x border-b border-base-300 rounded-b-xl',
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : '',
        'transition-all duration-300'
      ]"
    >
      <!-- Fullscreen Header (only shown in fullscreen mode) -->
      <div v-if="isFullscreen" class="bg-gradient-to-r from-primary/5 via-secondary/5 to-accent/5 border-b border-base-300 p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <Icon :name="module?.icon || 'cog'" size="md" class="text-primary" />
            <h2 class="text-lg font-bold text-base-content">{{ module?.name || 'Module' }}</h2>
          </div>
          <button
            @click="toggleFullscreen"
            class="btn btn-ghost btn-sm"
            title="Exit Fullscreen"
          >
            <Icon name="x" size="sm" />
          </button>
        </div>
      </div>

      <!-- Component Content -->
      <div :class="['p-4 sm:p-6', isFullscreen ? 'h-full overflow-auto' : '']">
        <!-- All components - let CSS handle modal conversion -->
        <div class="admin-component-wrapper">
          <component
            :is="component"
            :key="componentKey"
            @close="handleModalClose"
            @error="handleComponentError"
          />
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isRefreshing" class="absolute inset-0 bg-base-100/80 backdrop-blur-sm flex items-center justify-center rounded-xl">
      <div class="text-center space-y-2">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="text-sm text-base-content/70">Refreshing module...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Props
interface Props {
  component: any
  module: {
    id: string
    name: string
    description: string
    icon: string
  } | null
}

const props = defineProps<Props>()

// State
const isRefreshing = ref(false)
const isFullscreen = ref(false)
const componentKey = ref(0)

// Computed
const isModalComponent = computed(() => {
  // Check if the component is one of the modal-based admin components
  const modalComponents = [
    'UserManagement',
    'SystemSettings', 
    'DatabaseManagement',
    'SecuritySettings',
    'SystemLogs'
  ]
  
  return modalComponents.some(name => 
    props.component?.name?.includes(name) || 
    props.component?.__name?.includes(name)
  )
})

// Methods
const refreshModule = async () => {
  isRefreshing.value = true
  
  try {
    // Force component re-render
    componentKey.value++
    
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log(`🔄 Module refreshed: ${props.module?.name}`)
  } catch (error) {
    console.error('Failed to refresh module:', error)
  } finally {
    isRefreshing.value = false
  }
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  // Handle body scroll when in fullscreen
  if (isFullscreen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

const handleModalClose = () => {
  // Handle modal close events from converted components
  console.log('Modal close event received - ignoring in new layout')
}

const handleComponentError = (error: any) => {
  console.error('Component error in AdminModuleWrapper:', error)
  console.log('Module:', props.module?.name)
  console.log('Component:', props.component)
}

// Cleanup on unmount
import { onUnmounted } from 'vue'
onUnmounted(() => {
  // Restore body scroll if component is destroyed while in fullscreen
  if (isFullscreen.value) {
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
.admin-module-wrapper {
  position: relative;
  min-height: 400px;
}

/* Convert modal components to regular components */
:deep(.modal) {
  position: static !important;
  background: none !important;
  padding: 0 !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

:deep(.modal-box) {
  max-width: none !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 1.5rem !important;
  background: hsl(var(--b1)) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;
  border-radius: 0.75rem !important;
  transform: none !important;
  border: 1px solid hsl(var(--bc) / 0.1) !important;
}

:deep(.modal-action) {
  margin-top: 1.5rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid hsl(var(--bc) / 0.1) !important;
  justify-content: flex-end !important;
  gap: 0.5rem !important;
}

/* Hide close buttons from modal headers since we have our own controls */
:deep(.modal-box > div:first-child .btn-circle) {
  display: none !important;
}

/* Ensure modal headers look good */
:deep(.modal-box > div:first-child) {
  margin-bottom: 1.5rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 1px solid hsl(var(--bc) / 0.1) !important;
}

/* Fix tab functionality - ensure tabs are clickable and styled properly */
:deep(.tabs) {
  margin-bottom: 1.5rem !important;
}

:deep(.tabs .tab) {
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border: 1px solid hsl(var(--bc) / 0.2) !important;
  background: hsl(var(--b2)) !important;
}

:deep(.tabs .tab:hover) {
  background-color: hsl(var(--b3)) !important;
  border-color: hsl(var(--bc) / 0.3) !important;
}

:deep(.tabs .tab-active) {
  background-color: hsl(var(--p)) !important;
  color: hsl(var(--pc)) !important;
  border-color: hsl(var(--p)) !important;
}

/* Ensure form elements work properly */
:deep(.form-control) {
  margin-bottom: 1rem !important;
}

:deep(.btn) {
  cursor: pointer !important;
}

/* Fix any layout issues */
:deep(.card) {
  margin-bottom: 1rem !important;
}

:deep(.stats) {
  margin-bottom: 1.5rem !important;
}

/* Ensure proper spacing for extracted modal content */
.modal-content-extracted {
  /* Override modal styles */
}

/* Fullscreen styles */
.admin-module-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  background: hsl(var(--b1));
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .admin-module-wrapper {
    min-height: 300px;
  }
}
</style>
