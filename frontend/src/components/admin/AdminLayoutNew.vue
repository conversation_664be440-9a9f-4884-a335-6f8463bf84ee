<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
    <!-- Loading Overlay -->
    <div v-if="isLoadingModule" class="fixed inset-0 bg-base-100/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="text-center space-y-4">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="text-base-content/70">Loading {{ loadingModuleName }}...</p>
      </div>
    </div>

    <!-- Header -->
    <header class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-2 bg-primary/20 rounded-lg">
              <Icon name="cog" size="lg" class="text-primary" />
            </div>
            <div>
              <h1 class="text-2xl font-bold text-base-content">Admin Dashboard</h1>
              <p class="text-sm text-base-content/60">{{ currentModuleDescription }}</p>
            </div>
          </div>
          
          <!-- Module Navigation -->
          <nav class="hidden lg:flex items-center space-x-2">
            <button
              v-for="module in adminModules"
              :key="module.id"
              @click="navigateToModule(module)"
              :class="[
                'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                currentModule?.id === module.id
                  ? 'bg-primary text-primary-content shadow-lg'
                  : 'bg-base-200/50 text-base-content hover:bg-base-200 hover:shadow-md'
              ]"
            >
              <Icon :name="module.icon" size="sm" class="mr-2" />
              {{ module.name }}
            </button>
          </nav>

          <!-- Mobile Menu Button -->
          <button
            @click="showMobileMenu = !showMobileMenu"
            class="lg:hidden p-2 rounded-lg bg-base-200/50 hover:bg-base-200"
          >
            <Icon name="menu" size="md" />
          </button>
        </div>
      </div>
    </header>

    <!-- Mobile Navigation -->
    <div v-if="showMobileMenu" class="lg:hidden bg-base-100 border-b border-base-300 sticky top-16 z-30">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="grid grid-cols-2 gap-2">
          <button
            v-for="module in adminModules"
            :key="module.id"
            @click="navigateToModule(module); showMobileMenu = false"
            :class="[
              'p-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center',
              currentModule?.id === module.id
                ? 'bg-primary text-primary-content'
                : 'bg-base-200/50 text-base-content hover:bg-base-200'
            ]"
          >
            <Icon :name="module.icon" size="sm" class="mr-2" />
            {{ module.name }}
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-6 py-8">
      <!-- Module Content -->
      <div class="space-y-8">
        <!-- Current Module Component -->
        <Suspense>
          <template #default>
            <component 
              v-if="currentModuleComponent" 
              :is="currentModuleComponent"
              :key="currentModule?.id"
            />
          </template>
          <template #fallback>
            <div class="flex items-center justify-center py-12">
              <div class="text-center space-y-4">
                <div class="loading loading-spinner loading-lg text-primary"></div>
                <p class="text-base-content/70">Loading module...</p>
              </div>
            </div>
          </template>
        </Suspense>

        <!-- Fallback Content -->
        <div v-if="!currentModule" class="text-center py-12">
          <div class="space-y-4">
            <div class="text-6xl">🚀</div>
            <h2 class="text-2xl font-bold text-base-content">Welcome to the New Admin Dashboard</h2>
            <p class="text-base-content/70 max-w-2xl mx-auto">
              Select a module from the navigation above to get started. Each module is loaded on-demand for optimal performance.
            </p>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-base-200/50 border-t border-base-300 mt-12">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between text-sm text-base-content/60">
          <div>
            Admin Dashboard v2.0 • Code Splitting Enabled
          </div>
          <div class="flex items-center space-x-4">
            <span>Current Module: {{ currentModule?.name || 'None' }}</span>
            <span>•</span>
            <span>Loaded Modules: {{ loadedModules.size }}</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, defineAsyncComponent, type Component } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'

// Types
interface AdminModule {
  id: string
  name: string
  description: string
  icon: string
  component: () => Promise<Component>
  requiresAuth?: boolean
  requiresAdmin?: boolean
}

// Router and Store
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// State
const currentModule = ref<AdminModule | null>(null)
const currentModuleComponent = ref<Component | null>(null)
const isLoadingModule = ref(false)
const loadingModuleName = ref('')
const showMobileMenu = ref(false)
const loadedModules = ref(new Set<string>())

// Admin modules configuration with lazy loading
const adminModules: AdminModule[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    description: 'System overview and real-time metrics',
    icon: 'chart-bar',
    component: () => import('@/components/admin/AdminDashboard.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'users',
    name: 'Users',
    description: 'User management and authentication',
    icon: 'users',
    component: () => import('@/components/admin/UserManagement.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'system',
    name: 'System',
    description: 'System settings and configuration',
    icon: 'cog',
    component: () => import('@/components/admin/SystemSettings.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'database',
    name: 'Database',
    description: 'Database management and operations',
    icon: 'database',
    component: () => import('@/components/admin/DatabaseManagement.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'security',
    name: 'Security',
    description: 'Security settings and monitoring',
    icon: 'shield-check',
    component: () => import('@/components/admin/SecuritySettings.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'logs',
    name: 'Logs',
    description: 'System logs and monitoring',
    icon: 'document-text',
    component: () => import('@/components/admin/SystemLogs.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'email',
    name: 'Email',
    description: 'Email worker and queue management',
    icon: 'mail',
    component: () => import('@/components/admin/EmailWorkerControl.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'socket',
    name: 'Socket',
    description: 'Real-time connection monitoring',
    icon: 'wifi',
    component: () => import('@/components/admin/SocketMonitor.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'seo',
    name: 'SEO',
    description: 'SEO management and optimization',
    icon: 'search',
    component: () => import('@/components/admin/SEOManager.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'notifications',
    name: 'Notifications',
    description: 'Push notification management',
    icon: 'bell',
    component: () => import('@/components/admin/PushNotificationSender.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'performance',
    name: 'Performance',
    description: 'Performance monitoring and optimization',
    icon: 'chart-bar',
    component: () => import('@/components/admin/AdminPerformanceMonitor.vue'),
    requiresAuth: true,
    requiresAdmin: true
  }
]

// Computed
const currentModuleDescription = computed(() => {
  return currentModule.value?.description || 'Select a module to get started'
})

// Methods
const navigateToModule = (module: AdminModule) => {
  // Navigate to the module route
  router.push(`/admin/new/${module.id}`)
}

const loadModule = async (module: AdminModule) => {
  // Check authorization
  if (module.requiresAuth && !authStore.isAuthenticated) {
    console.warn('Module requires authentication')
    return
  }
  
  if (module.requiresAdmin && !authStore.isAdmin) {
    console.warn('Module requires admin privileges')
    return
  }

  // Don't reload if already current
  if (currentModule.value?.id === module.id && currentModuleComponent.value) {
    return
  }

  try {
    isLoadingModule.value = true
    loadingModuleName.value = module.name
    
    console.log(`🔄 Loading admin module: ${module.name}`)
    
    // Load the component
    const component = await module.component()
    
    // Update state
    currentModule.value = module
    currentModuleComponent.value = component.default || component
    loadedModules.value.add(module.id)
    
    console.log(`✅ Admin module loaded: ${module.name}`)
  } catch (error) {
    console.error(`❌ Failed to load admin module: ${module.name}`, error)
  } finally {
    isLoadingModule.value = false
    loadingModuleName.value = ''
  }
}

// Load module based on route or default
const loadModuleFromRoute = () => {
  const routeModule = route.meta?.adminModule as string
  if (routeModule) {
    const module = adminModules.find(m => m.id === routeModule)
    if (module) {
      loadModule(module)
      return
    }
  }

  // Load dashboard by default
  const dashboardModule = adminModules.find(m => m.id === 'dashboard')
  if (dashboardModule) {
    loadModule(dashboardModule)
  }
}

// Watch route changes to load appropriate module
watch(() => route.meta?.adminModule, () => {
  loadModuleFromRoute()
}, { immediate: false })

// Load default module on mount
onMounted(() => {
  loadModuleFromRoute()
})
</script>

<style scoped>
/* Custom styles for the new admin layout */
.admin-layout {
  /* Add any custom styles here */
}
</style>
