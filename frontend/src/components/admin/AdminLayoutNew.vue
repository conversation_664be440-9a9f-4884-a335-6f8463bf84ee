<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
    <!-- Loading Overlay -->
    <div v-if="isLoadingModule" class="fixed inset-0 bg-base-100/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="text-center space-y-4">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="text-base-content/70">Loading {{ loadingModuleName }}...</p>
      </div>
    </div>

    <!-- Improved Header with better spacing and mobile support -->
    <header class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300 sticky top-0 z-40 backdrop-blur-md">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <!-- Main Header Row -->
        <div class="flex items-center justify-between py-4">
          <!-- Logo and Title -->
          <div class="flex items-center space-x-3 min-w-0 flex-1">
            <div class="p-2 bg-primary/20 rounded-lg flex-shrink-0">
              <Icon name="cog" size="lg" class="text-primary" />
            </div>
            <div class="min-w-0">
              <h1 class="text-xl sm:text-2xl font-bold text-base-content truncate">Admin Dashboard</h1>
              <p class="text-xs sm:text-sm text-base-content/60 truncate">{{ currentModuleDescription }}</p>
            </div>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden xl:flex items-center space-x-1 flex-shrink-0">
            <button
              v-for="module in adminModules.slice(0, 6)"
              :key="module.id"
              @click="navigateToModule(module)"
              :class="[
                'px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center',
                currentModule?.id === module.id
                  ? 'bg-primary text-primary-content shadow-lg'
                  : 'bg-base-200/50 text-base-content hover:bg-base-200 hover:shadow-md'
              ]"
            >
              <Icon :name="module.icon" size="sm" class="mr-1" />
              <span class="hidden 2xl:inline">{{ module.name }}</span>
            </button>

            <!-- More Menu for additional modules -->
            <div v-if="adminModules.length > 6" class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-sm bg-base-200/50 hover:bg-base-200">
                <Icon name="ellipsis-horizontal" size="sm" />
                <span class="hidden 2xl:inline ml-1">More</span>
              </div>
              <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-52 border border-base-300">
                <li v-for="module in adminModules.slice(6)" :key="module.id">
                  <button
                    @click="navigateToModule(module)"
                    :class="[
                      'flex items-center',
                      currentModule?.id === module.id ? 'active' : ''
                    ]"
                  >
                    <Icon :name="module.icon" size="sm" class="mr-2" />
                    {{ module.name }}
                  </button>
                </li>
              </ul>
            </div>
          </nav>

          <!-- Mobile Menu Button -->
          <button
            @click="toggleMobileMenu"
            class="xl:hidden p-2 rounded-lg bg-base-200/50 hover:bg-base-200 transition-colors flex-shrink-0"
            :class="{ 'bg-primary text-primary-content': showMobileMenu }"
          >
            <Icon :name="showMobileMenu ? 'x' : 'menu'" size="md" />
          </button>
        </div>

        <!-- Mobile/Tablet Navigation Tabs -->
        <div class="xl:hidden border-t border-base-300/50">
          <div class="flex overflow-x-auto py-2 space-x-1 scrollbar-hide">
            <button
              v-for="module in adminModules"
              :key="module.id"
              @click="navigateToModule(module)"
              :class="[
                'flex-shrink-0 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center whitespace-nowrap',
                currentModule?.id === module.id
                  ? 'bg-primary text-primary-content shadow-lg'
                  : 'bg-base-200/30 text-base-content hover:bg-base-200/50'
              ]"
            >
              <Icon :name="module.icon" size="sm" class="mr-2" />
              {{ module.name }}
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Mobile Navigation Overlay -->
    <div
      v-if="showMobileMenu"
      class="xl:hidden fixed inset-0 bg-black/50 z-30"
      @click="closeMobileMenu"
    ></div>

    <!-- Mobile Navigation Sidebar -->
    <div
      v-if="showMobileMenu"
      class="xl:hidden fixed top-0 right-0 h-full w-80 bg-base-100 shadow-2xl z-40 transform transition-transform duration-300 ease-in-out"
    >
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-bold text-base-content">Admin Modules</h2>
          <button
            @click="closeMobileMenu"
            class="btn btn-ghost btn-circle btn-sm"
          >
            <Icon name="x" size="md" />
          </button>
        </div>

        <div class="space-y-2">
          <button
            v-for="module in adminModules"
            :key="module.id"
            @click="navigateToModule(module); closeMobileMenu()"
            :class="[
              'w-full p-3 rounded-lg text-left font-medium transition-all duration-200 flex items-center',
              currentModule?.id === module.id
                ? 'bg-primary text-primary-content shadow-lg'
                : 'bg-base-200/30 text-base-content hover:bg-base-200'
            ]"
          >
            <Icon :name="module.icon" size="sm" class="mr-3" />
            <div>
              <div class="font-medium">{{ module.name }}</div>
              <div class="text-xs opacity-70">{{ module.description }}</div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content with proper spacing to avoid navbar overlap -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
      <!-- Module Content -->
      <div class="space-y-6 sm:space-y-8">
        <!-- Current Module Component Wrapper -->
        <div class="admin-module-wrapper">
          <Suspense>
            <template #default>
              <AdminModuleWrapper
                v-if="currentModuleComponent"
                :component="currentModuleComponent"
                :module="currentModule"
                :key="currentModule?.id"
              />
            </template>
            <template #fallback>
              <div class="flex items-center justify-center py-12">
                <div class="text-center space-y-4">
                  <div class="loading loading-spinner loading-lg text-primary"></div>
                  <p class="text-base-content/70">Loading module...</p>
                </div>
              </div>
            </template>
          </Suspense>
        </div>

        <!-- Fallback Content -->
        <div v-if="!currentModule" class="text-center py-12">
          <div class="space-y-6">
            <div class="text-6xl">🚀</div>
            <h2 class="text-2xl sm:text-3xl font-bold text-base-content">Welcome to the New Admin Dashboard</h2>
            <p class="text-base-content/70 max-w-2xl mx-auto text-sm sm:text-base">
              Select a module from the navigation above to get started. Each module is loaded on-demand for optimal performance.
            </p>

            <!-- Quick Access Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-8 max-w-4xl mx-auto">
              <div
                v-for="module in adminModules.slice(0, 6)"
                :key="module.id"
                @click="navigateToModule(module)"
                class="card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border border-base-300 hover:border-primary/30"
              >
                <div class="card-body p-4 text-center">
                  <Icon :name="module.icon" size="xl" class="text-primary mx-auto mb-2" />
                  <h3 class="font-semibold text-base-content">{{ module.name }}</h3>
                  <p class="text-xs text-base-content/60">{{ module.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-base-200/50 border-t border-base-300 mt-12">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between text-sm text-base-content/60">
          <div>
            Admin Dashboard v2.0 • Code Splitting Enabled
          </div>
          <div class="flex items-center space-x-4">
            <span>Current Module: {{ currentModule?.name || 'None' }}</span>
            <span>•</span>
            <span>Loaded Modules: {{ loadedModules.size }}</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, defineAsyncComponent, type Component } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'
import AdminModuleWrapper from './AdminModuleWrapper.vue'

// Types
interface AdminModule {
  id: string
  name: string
  description: string
  icon: string
  component: () => Promise<Component>
  requiresAuth?: boolean
  requiresAdmin?: boolean
}

// Router and Store
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// State
const currentModule = ref<AdminModule | null>(null)
const currentModuleComponent = ref<Component | null>(null)
const isLoadingModule = ref(false)
const loadingModuleName = ref('')
const showMobileMenu = ref(false)
const loadedModules = ref(new Set<string>())

// Admin modules configuration with lazy loading
const adminModules: AdminModule[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    description: 'System overview and real-time metrics',
    icon: 'chart-bar',
    component: () => import('@/components/admin/AdminDashboard.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'users',
    name: 'Users',
    description: 'User management and authentication',
    icon: 'users',
    component: () => import('@/components/admin/UserManagement.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'system',
    name: 'System',
    description: 'System settings and configuration',
    icon: 'cog',
    component: () => import('@/components/admin/SystemSettings.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'database',
    name: 'Database',
    description: 'Database management and operations',
    icon: 'database',
    component: () => import('@/components/admin/DatabaseManagement.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'security',
    name: 'Security',
    description: 'Security settings and monitoring',
    icon: 'shield-check',
    component: () => import('@/components/admin/SecuritySettings.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'logs',
    name: 'Logs',
    description: 'System logs and monitoring',
    icon: 'document-text',
    component: () => import('@/components/admin/SystemLogs.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'email',
    name: 'Email',
    description: 'Email worker and queue management',
    icon: 'mail',
    component: () => import('@/components/admin/EmailWorkerControl.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'socket',
    name: 'Socket',
    description: 'Real-time connection monitoring',
    icon: 'wifi',
    component: () => import('@/components/admin/SocketMonitor.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'seo',
    name: 'SEO',
    description: 'SEO management and optimization',
    icon: 'search',
    component: () => import('@/components/admin/SEOManager.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'notifications',
    name: 'Notifications',
    description: 'Push notification management',
    icon: 'bell',
    component: () => import('@/components/admin/PushNotificationSender.vue'),
    requiresAuth: true,
    requiresAdmin: true
  },
  {
    id: 'performance',
    name: 'Performance',
    description: 'Performance monitoring and optimization',
    icon: 'chart-bar',
    component: () => import('@/components/admin/AdminPerformanceMonitor.vue'),
    requiresAuth: true,
    requiresAdmin: true
  }
]

// Computed
const currentModuleDescription = computed(() => {
  return currentModule.value?.description || 'Select a module to get started'
})

// Methods
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const navigateToModule = (module: AdminModule) => {
  // Navigate to the module route
  router.push(`/admin/new/${module.id}`)
}

const loadModule = async (module: AdminModule) => {
  // Check authorization
  if (module.requiresAuth && !authStore.isAuthenticated) {
    console.warn('Module requires authentication')
    return
  }
  
  if (module.requiresAdmin && !authStore.isAdmin) {
    console.warn('Module requires admin privileges')
    return
  }

  // Don't reload if already current
  if (currentModule.value?.id === module.id && currentModuleComponent.value) {
    return
  }

  try {
    isLoadingModule.value = true
    loadingModuleName.value = module.name
    
    console.log(`🔄 Loading admin module: ${module.name}`)
    
    // Load the component
    const component = await module.component()
    
    // Update state
    currentModule.value = module
    currentModuleComponent.value = component.default || component
    loadedModules.value.add(module.id)
    
    console.log(`✅ Admin module loaded: ${module.name}`)
  } catch (error) {
    console.error(`❌ Failed to load admin module: ${module.name}`, error)
  } finally {
    isLoadingModule.value = false
    loadingModuleName.value = ''
  }
}

// Load module based on route or default
const loadModuleFromRoute = () => {
  const routeModule = route.meta?.adminModule as string
  if (routeModule) {
    const module = adminModules.find(m => m.id === routeModule)
    if (module) {
      loadModule(module)
      return
    }
  }

  // Load dashboard by default
  const dashboardModule = adminModules.find(m => m.id === 'dashboard')
  if (dashboardModule) {
    loadModule(dashboardModule)
  }
}

// Watch route changes to load appropriate module
watch(() => route.meta?.adminModule, () => {
  loadModuleFromRoute()
}, { immediate: false })

// Load default module on mount
onMounted(() => {
  loadModuleFromRoute()
})
</script>

<style scoped>
/* Custom styles for the new admin layout */
.admin-layout {
  /* Add any custom styles here */
}

/* Hide scrollbar for horizontal scroll */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Smooth transitions for mobile menu */
.admin-module-wrapper {
  min-height: 200px;
}

/* Ensure proper spacing on mobile */
@media (max-width: 640px) {
  .admin-module-wrapper {
    margin-top: 0;
  }
}

/* Custom dropdown styling */
.dropdown-content {
  backdrop-filter: blur(8px);
}
</style>
