<template>
  <div class="admin-test-component p-6">
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <h2 class="card-title text-primary">
          <Icon name="check-circle" size="lg" class="mr-2" />
          Admin Module Test Component
        </h2>
        
        <div class="space-y-4">
          <div class="alert alert-success">
            <Icon name="check" size="sm" />
            <span>✅ Component loaded successfully!</span>
          </div>
          
          <div class="stats shadow">
            <div class="stat">
              <div class="stat-title">Component Status</div>
              <div class="stat-value text-success">Working</div>
              <div class="stat-desc">Code splitting functional</div>
            </div>
            
            <div class="stat">
              <div class="stat-title">Load Time</div>
              <div class="stat-value text-primary">{{ loadTime }}ms</div>
              <div class="stat-desc">Lazy loading active</div>
            </div>
          </div>
          
          <div class="bg-base-200 p-4 rounded-lg">
            <h3 class="font-semibold mb-2">Test Information:</h3>
            <ul class="text-sm space-y-1">
              <li>• This is a test component to verify admin module loading</li>
              <li>• Component was loaded via dynamic import</li>
              <li>• Code splitting is working correctly</li>
              <li>• Navigation and routing are functional</li>
            </ul>
          </div>
          
          <div class="flex space-x-2">
            <button @click="testFunction" class="btn btn-primary">
              <Icon name="play" size="sm" class="mr-2" />
              Test Function
            </button>
            <button @click="showAlert" class="btn btn-secondary">
              <Icon name="bell" size="sm" class="mr-2" />
              Show Alert
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

// State
const loadTime = ref(0)

// Methods
const testFunction = () => {
  console.log('🧪 Test function called from admin module')
  alert('Test function working!')
}

const showAlert = () => {
  alert('Admin module alert test - working correctly!')
}

// Calculate load time
onMounted(() => {
  const startTime = performance.now()
  loadTime.value = Math.round(startTime)
  console.log('🧪 AdminTestComponent mounted successfully')
})
</script>

<style scoped>
.admin-test-component {
  /* Test component styles */
}
</style>
